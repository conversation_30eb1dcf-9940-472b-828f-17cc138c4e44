#include <Arduino.h>


struct Note {
  String color;
  byte inputPin;
  int order;
  bool played;
};
Note notes[] = {{"Red", 32, 0, false},
                    // {27, 32, 3, false},
                    // {26, 35, 2, false},
                    {"Orange", 33, 1, false}
                  };
const byte totalNotes = sizeof(notes) / sizeof(notes[0]);

const int threshold = 20;

int currentNoteIndex();
int lastNoteIndex();
int nextNoteIndex();


void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);
}

void loop() {

  for (Note &note : notes) {
    if (touchRead(note.inputPin) < threshold) {
    Serial.println(note.color);
    }
  }

  int currentNote = currentNoteIndex();
  int lastNote = lastNoteIndex();
  int nextNote = nextNoteIndex();

  if (nextNote < 0) {
    Serial.println("All notes played");
    return;
  }

  if (currentNote < 0) {
    return;
  }

  if (currentNote == nextNote) {
    Serial.println("Correct Note Played");
    notes[currentNote].played = true;
    return;
  }

  if (currentNote == lastNote) {
    Serial.println("Same note played");
    return;
  }

  Serial.println("Wrong note played");
   for (Note &note : notes) {
    note.played = false;
  }

}

// Note currentNote() {
//    for (Note &note : notes) {
//     if (touchRead(note.inputPin) < threshold) {
//      return note;
//     }
//   }
// }

// Note lastNote() {
//   Note lastNote = notes[0];
//   for (Note &note : notes) {
//     if (note.played) {
//       lastNote = note;
//     }
//   }

//   return lastNote;
// }

// Note nextNote() {
//   Note nextNote = notes[0];
//   for (Note &note : notes) {
//     if (!note.played) {
//       nextNote = note;
//     }
//   }

//   return nextNote;
// }

int currentNoteIndex() {
  for (Note &note : notes) {
    if (touchRead(note.inputPin) < threshold) {
     return note.order;
    }
  }

  return -1;
}

int lastNoteIndex() {
  int last = -1;
  for (Note &note : notes) {
    if (note.played) {
      last = note.order;  // keep updating
    }
  }
  return last;
}

int nextNoteIndex() {
  for (Note &note : notes) {
    if (!note.played) {
      return note.order;
    }
  }
  return -1;
}

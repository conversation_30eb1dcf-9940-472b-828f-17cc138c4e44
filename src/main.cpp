#include <Arduino.h>

#include <optional>


struct Note {
  String color;
  byte inputPin;
  byte order;
  bool played;
};
Note notes[] = {{"Red", 32, 0, false},
                    // {27, 32, 3, false},
                    // {26, 35, 2, false},
                    {"Orange", 33, 1, false}
                  };
const byte totalNotes = sizeof(notes) / sizeof(notes[0]);

const int threshold = 20;
const int currentStep = 0;


void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);
}

void loop() {

  for (Note &note : notes) {
    if (touchRead(note.inputPin) < threshold) {
    Serial.println(note.color);
    }
  }
  delay(100);
  // put your main code here, to run repeatedly:
}

Note currentNote() {
  for (Note &note : notes) {
    if (touchRead(note.inputPin) < threshold) {
     return note;
    }
  }
}

Note lastNote() {
  Note lastNote = notes[0];
  for (Note &note : notes) {
    if (note.played) {
      lastNote = note;
    }
  }

  return lastNote;
}
Note nextNote() {
  for (Note &note : notes) {
    if (!note.played) {
      return note;
    }
  }
}

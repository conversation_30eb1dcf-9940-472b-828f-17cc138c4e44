#include <Arduino.h>

struct Note {
  String color;
  byte inputPin;
  byte order;
  bool played;
};

Note notes[] = {{"Red", 32, 0, false},
                    // {27, 32, 3, false},
                    // {26, 35, 2, false},
                    {"Orange", 33, 1, false}
                  };
const byte numNotes = sizeof(notes) / sizeof(notes[0]);

enum PuzzleState {
  Inactive,
  InProgress,
  Solved,
  Failed
};
PuzzleState puzzleState = Inactive;

unsigned long lastStateChange = 0;
unsigned long failIndicatorDuration = 2000;
unsigned long successIndicatorDuration = 2000;

const int threshold = 20;
int currentStep = 0;
int mistakesMade = 0;

void setupNotes();
void startPuzzle();
void runPuzzle();
void processNote(Note &note);
void puzzleSolved();
void puzzleFailed();
void deactivatePuzzle();
void updateState(PuzzleState newState);
void resetPuzzle();
int currentNoteIndex();
int lastNoteIndex();
int nextNoteIndex();


void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  setupNotes();
}

void setupNotes() {
  for (Note &note : notes) {
    pinMode(note.inputPin, INPUT);
  }
}

void loop() {
  switch (puzzleState) {
    case Inactive:
      // For now, start puzzle automatically - later you can add trigger condition
      startPuzzle();
      break;
    case InProgress:
      runPuzzle();
      break;
    case Solved:
      if (millis() - lastStateChange > successIndicatorDuration) {
        deactivatePuzzle();
      }
      break;
    case Failed:
      if (millis() - lastStateChange > failIndicatorDuration) {
        startPuzzle();
      }
      break;
  }
}

void runPuzzle() {
  for (Note &note : notes) {
    processNote(note);
  }

  if (currentStep == numNotes) {
    if (mistakesMade > 0) {
      puzzleFailed();
    } else {
      puzzleSolved();
    }
  }
}

void processNote(Note &note) {
  if (touchRead(note.inputPin) < threshold) {
    delay(20); // debounce
    if (touchRead(note.inputPin) < threshold) {
      Serial.println("Note touched: " + note.color);

      if (note.order == currentStep) {
        Serial.println("Correct note!");
        note.played = true;
        currentStep++;
        // TODO: Play success sound
      } else {
        Serial.println("Wrong note!");
        mistakesMade++;
        // TODO: Play failure sound
      }
    }
  }
}

void startPuzzle() {
  updateState(InProgress);
  resetPuzzle();
  Serial.println("Puzzle started!");
}

void puzzleFailed() {
  updateState(Failed);
  Serial.println("Puzzle failed!");
  // TODO: Play failure sound
}

void puzzleSolved() {
  updateState(Solved);
  Serial.println("Puzzle solved!");
  // TODO: Play success sound
}

void deactivatePuzzle() {
  updateState(Inactive);
  resetPuzzle();
  Serial.println("Puzzle deactivated");
}

void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}

void resetPuzzle() {
  for (Note &note : notes) {
    note.played = false;
  }
  currentStep = 0;
  mistakesMade = 0;
}

int currentNoteIndex() {
  for (Note &note : notes) {
    if (touchRead(note.inputPin) < threshold) {
     return note.order;
    }
  }
  return -1;
}

int lastNoteIndex() {
  int last = -1;
  for (Note &note : notes) {
    if (note.played) {
      last = note.order;
    }
  }
  return last;
}

int nextNoteIndex() {
  for (Note &note : notes) {
    if (!note.played) {
      return note.order;
    }
  }
  return -1;
}

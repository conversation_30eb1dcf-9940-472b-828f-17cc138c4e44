#include <Arduino.h>

struct Note {
  String color;
  byte inputPin;
  byte order;
  bool played;
};

const int buzzerPin = 21;     // GPIO21 for passive buzzer
Note notes[] = {{"Red", 32, 0, false},
                {"Orange", 33, 1, false},
                {"Yellow", 27, 2, false},
                {"Green", 14, 3, false},
                {"Blue", 12, 4, false},
                {"Purple", 13, 5, false}
                };
const byte numNotes = sizeof(notes) / sizeof(notes[0]);

enum PuzzleState {
  Inactive,
  InProgress,
  Solved,
  Failed
};
PuzzleState puzzleState = Inactive;

unsigned long lastStateChange = 0;
unsigned long failIndicatorDuration = 2000;
unsigned long successIndicatorDuration = 2000;

const int threshold = 20;

void setupNotes();
void startPuzzle();
void runPuzzle();
void puzzleSolved();
void puzzleFailed();
void deactivatePuzzle();
void updateState(PuzzleState newState);
void resetPuzzle();
void playSuccessSound();
void playFailureSound();
int currentNoteIndex();
int lastNoteIndex();
int nextNoteIndex();


void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  // Setup buzzer pin
  pinMode(buzzerPin, OUTPUT);

  setupNotes();
}

void setupNotes() {
  for (Note &note : notes) {
    pinMode(note.inputPin, INPUT);
  }
}

void loop() {
  tone(buzzerPin, 1000, 500);  // Play 1kHz tone for 500ms
  delay(1000);                 // Wait 1 second between beeps

  // switch (puzzleState) {
  //   case Inactive:
  //     // For now, start puzzle automatically - later you can add trigger condition
  //     startPuzzle();
  //     break;
  //   case InProgress:
  //     runPuzzle();
  //     break;
  //   case Solved:
  //     if (millis() - lastStateChange > successIndicatorDuration) {
  //       startPuzzle();
  //     }
  //     break;
  //   case Failed:
  //     if (millis() - lastStateChange > failIndicatorDuration) {
  //       startPuzzle();
  //     }
  //     break;
  // }
}

void runPuzzle() {
  int currentNote = currentNoteIndex();
  int lastNote = lastNoteIndex();
  int nextNote = nextNoteIndex();

  if (nextNote < 0) {
    Serial.println("All notes played");
    puzzleSolved();
    return;
  }

  if (currentNote < 0) {
    return;
  }

  if (currentNote == nextNote) {
    Serial.println("Correct Note Played");
    notes[currentNote].played = true;
    playSuccessSound();
    return;
  }

  if (currentNote == lastNote) {
    return;
  }

  Serial.println("Wrong note played");
  playFailureSound();
  for (Note &note : notes) {
    note.played = false;
  }
  puzzleFailed();
}



void startPuzzle() {
  updateState(InProgress);
  resetPuzzle();
  Serial.println("Puzzle started!");
}

void puzzleFailed() {
  updateState(Failed);
  Serial.println("Puzzle failed!");
  // TODO: Play failure sound
}

void puzzleSolved() {
  updateState(Solved);
  Serial.println("Puzzle solved!");
  // TODO: Play success sound
}

void deactivatePuzzle() {
  updateState(Inactive);
  resetPuzzle();
  Serial.println("Puzzle deactivated");
}

void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}

void resetPuzzle() {
  for (Note &note : notes) {
    note.played = false;
  }
}

int currentNoteIndex() {
  for (Note &note : notes) {
    if (touchRead(note.inputPin) < threshold) {
    // Serial.println(note.color);
     return note.order;
    }
  }
  return -1;
}

int lastNoteIndex() {
  int last = -1;
  for (Note &note : notes) {
    if (note.played) {
      last = note.order;
    }
  }
  return last;
}

int nextNoteIndex() {
  for (Note &note : notes) {
    if (!note.played) {
      return note.order;
    }
  }
  return -1;
}

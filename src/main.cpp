#include <Arduino.h>
#include <FastLED.h>

struct Note {
  String color;
  byte inputPin;
  int tone;
  byte ledIndex;
  bool played;
};

int melody[] = { 262, 294, 330, 349, 392, 440, 494, 523 };  // C D E F G A B C


const int buzzerPin = 21;     // GPIO21 for buzzer
const int ledcChannel = 0;    // One of 0–15
const int resolution = 100;

// LED Strip configuration
#define LED_PIN 25           // GPIO25 for LED strip data
#define NUM_LEDS 6          // Number of LEDs (one per note)
#define LED_TYPE WS2812B    // Common addressable LED type
#define COLOR_ORDER GRB     // Color order for WS2812B
CRGB leds[NUM_LEDS];
Note notes[] = {{"Blue", 12, 392, 0, false},
                {"Yellow", 27, 330, 1, false},
                {"Green", 14, 349, 2, false},
                {"Red", 32, 262, 3, false},
                {"Purple", 13, 440, 4, false},
                {"Orange", 33, 294, 5, false},};
const byte numNotes = sizeof(notes) / sizeof(notes[0]);

enum PuzzleState {
  Inactive,
  InProgress,
  Solved,
  Failed
};
PuzzleState puzzleState = Inactive;

unsigned long lastStateChange = 0;
unsigned long failIndicatorDuration = 2000;
unsigned long successIndicatorDuration = 2000;

const int threshold = 20;

void setupNotes();
void setupLEDs();
void startPuzzle();
void runPuzzle();
void puzzleSolved();
void puzzleFailed();
void deactivatePuzzle();
void updateState(PuzzleState newState);
void resetPuzzle();
void playUglyFailureSound();
void showNoteColor(int noteIndex);
void clearLEDs();
void showSuccessPattern();
int currentNoteIndex();
int lastNoteIndex();
int nextNoteIndex();
int getNextNoteIndex();
void playStringNote(int frequency, int duration);
void playStringChord();
void playStringChordWithLights();
Note getNoteByOrder(int order);


void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  ledcSetup(ledcChannel, 2000, resolution);  // 2kHz tone, 8-bit resolution
  ledcAttachPin(buzzerPin, ledcChannel);

  setupNotes();
  setupLEDs();
}

void setupNotes() {
  for (Note &note : notes) {
    pinMode(note.inputPin, INPUT);
  }
}

void loop() {
  // ledcWriteTone(ledcChannel, 3000);  // Play 1kHz tone
  // delay(500);                        // Beep duration
  // ledcWriteTone(ledcChannel, 0);     // Stop sound
  // delay(1000);


  switch (puzzleState) {
    case Inactive:
      // For now, start puzzle automatically - later you can add trigger condition
      startPuzzle();
      break;
    case InProgress:
      runPuzzle();
      break;
    case Solved:
      if (millis() - lastStateChange > successIndicatorDuration) {
        startPuzzle();
      }
      break;
    case Failed:
      if (millis() - lastStateChange > failIndicatorDuration) {
        startPuzzle();
      }
      break;
  }
}

void runPuzzle() {
  // Check for any note being touched
  for (int i = 0; i < numNotes; i++) {
    if (touchRead(notes[i].inputPin) < threshold) {
      // Always play the note and show its color
      showNoteColor(notes[i].ledIndex);
      ledcWriteTone(ledcChannel, notes[i].tone);
      delay(300);
      ledcWriteTone(ledcChannel, 0);
      clearLEDs();

      // Check if this is the correct next note
      int nextNoteIndex = getNextNoteIndex();

      if (nextNoteIndex < 0) {
        // All notes already played - puzzle solved
        puzzleSolved();
        return;
      }

      if (i == nextNoteIndex) {
        Serial.println("Correct Note Played: " + notes[i].color);
        notes[i].played = true;

        // Check if puzzle is now complete
        if (getNextNoteIndex() < 0) {
          puzzleSolved();
        }
      } else {
        Serial.println("Wrong note played: " + notes[i].color);
        playUglyFailureSound();
        resetPuzzle();  // Reset progress
        puzzleFailed();
      }
      return; // Only process one note at a time
    }
  }
}



void startPuzzle() {
  updateState(InProgress);
  resetPuzzle();
  Serial.println("Puzzle started!");
}

void puzzleFailed() {
  updateState(Failed);
  Serial.println("Puzzle failed!");
}

void puzzleSolved() {
  updateState(Solved);
  Serial.println("Puzzle solved!");

  // Play through all correct notes again
  for (int i = 0; i < numNotes; i++) {
    showNoteColor(notes[i].ledIndex);   // Show LED color
    ledcWriteTone(ledcChannel, notes[i].tone);  // Play tone
    delay(500);                         // Note duration
    ledcWriteTone(ledcChannel, 0);      // Stop sound
    clearLEDs();                        // Clear LED
    // delay(100);                         // Brief pause between notes
  }

  playStringChordWithLights();        // Play chord with LED flourish simultaneously
}

void deactivatePuzzle() {
  updateState(Inactive);
  resetPuzzle();
  Serial.println("Puzzle deactivated");
}

void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}

void resetPuzzle() {
  for (Note &note : notes) {
    note.played = false;
  }
}

int currentNoteIndex() {
  for (int i = 0; i < numNotes; i++) {
    if (touchRead(notes[i].inputPin) < threshold) {
      return i;
    }
  }
  return -1;
}

int getNextNoteIndex() {
  for (int i = 0; i < numNotes; i++) {
    if (!notes[i].played) {
      return i;
    }
  }
  return -1;  // All notes played
}

int lastNoteIndex() {
  int last = -1;
  for (int i = 0; i < numNotes; i++) {
    if (notes[i].played) {
      last = i;
    }
  }
  return last;
}

int nextNoteIndex() {
  return getNextNoteIndex();  // Same as getNextNoteIndex
}

Note getNoteByOrder(int order) {
  if (order >= 0 && order < numNotes) {
    return notes[order];
  }
  return Note{"", 0, 0, 0, false};
}


void playStringNote(int frequency, int duration) {
  // Simulate a plucked string with attack, sustain, and decay

  // Attack phase - start loud and bright
  ledcWriteTone(ledcChannel, frequency);
  delay(50);  // Initial bright attack

  // Add slight vibrato and natural decay
  int steps = duration / 50;
  for(int i = 0; i < steps; i++) {
    // Add subtle vibrato (frequency modulation)
    int vibrato = (i % 4 < 2) ? frequency + 2 : frequency - 2;
    ledcWriteTone(ledcChannel, vibrato);
    delay(50);
  }

  // Decay phase - fade out naturally
  ledcWriteTone(ledcChannel, 0);
  delay(100);  // Brief silence between notes
}

void playStringChord() {
  // Play a beautiful major chord arpeggio (like strumming)
  int chord[] = {262, 330, 392, 523};  // C major chord (C-E-G-C)

  // Strum the chord (quick succession)
  for(int i = 0; i < 4; i++) {
    ledcWriteTone(ledcChannel, chord[i]);
    delay(80);  // Quick strum timing
  }

  // Let the final note ring out with natural decay
  playStringNote(523, 800);  // High C with natural string decay
}

void playStringChordWithLights() {
  // Play a beautiful major chord arpeggio (like strumming) with LED colors
  int chord[] = {262, 330, 392, 523};  // C major chord (C-E-G-C)

  // Start the LED color sequence at the same time as the chord
  int ledIndex = 0;

  // Strum the chord (quick succession)
  for(int i = 0; i < 4; i++) {
    ledcWriteTone(ledcChannel, chord[i]);

    // Show one LED color per chord note
    if(ledIndex < NUM_LEDS) {
      clearLEDs();
      showNoteColor(notes[ledIndex].ledIndex);
      ledIndex++;
    }

    delay(80);  // Quick strum timing
  }

  // Continue showing remaining LED colors during the sustained note
  // Let the final note ring out with natural decay
  playStringNote(523, 800);  // High C with natural string decay

  // Show remaining colors during the sustained note
  while(ledIndex < NUM_LEDS) {
    clearLEDs();
    showNoteColor(notes[ledIndex].ledIndex);
    ledIndex++;
    delay(100);  // Timing for remaining colors
  }

  clearLEDs();
}

void setupLEDs() {
  FastLED.addLeds<LED_TYPE, LED_PIN, COLOR_ORDER>(leds, NUM_LEDS);
  FastLED.setBrightness(100);  // Set brightness (0-255)
  clearLEDs();
}

void showNoteColor(int noteIndex) {
  clearLEDs();  // Clear all LEDs first

  // Map note colors to RGB values
  CRGB color;
  String noteColor = notes[noteIndex].color;

  if (noteColor == "Red") {
    color = CRGB::Red;
  } else if (noteColor == "Orange") {
    color = CRGB::Orange;
  } else if (noteColor == "Yellow") {
    color = CRGB::Yellow;
  } else if (noteColor == "Green") {
    color = CRGB::Green;
  } else if (noteColor == "Blue") {
    color = CRGB::Blue;
  } else if (noteColor == "Purple") {
    color = CRGB::Purple;
  } else {
    color = CRGB::White;  // Default color
  }

  // Light up the LED corresponding to the note
  leds[noteIndex] = color;
  FastLED.show();
}

void clearLEDs() {
  fill_solid(leds, NUM_LEDS, CRGB::Black);
  FastLED.show();
}

void playUglyFailureSound() {
  // Simulate a poorly strummed guitar note - muted, buzzy, and out of tune

  // Start with a muted "thunk" sound (like hitting muted strings)
  ledcWriteTone(ledcChannel, 80);    // Very low thud
  delay(50);
  ledcWriteTone(ledcChannel, 0);     // Brief silence
  delay(20);

  // Buzzy, out-of-tune note that wavers (like poor finger placement)
  ledcWriteTone(ledcChannel, 220);   // Slightly flat A
  delay(100);
  ledcWriteTone(ledcChannel, 215);   // Even flatter
  delay(80);
  ledcWriteTone(ledcChannel, 225);   // Sharp again
  delay(90);
  ledcWriteTone(ledcChannel, 210);   // Very flat
  delay(120);

  // Add some fret buzz (rapid low frequency noise)
  for(int i = 0; i < 8; i++) {
    ledcWriteTone(ledcChannel, 60 + (i * 5));  // Low buzzing frequencies
    delay(25);
  }

  // End with a dead, muted string sound
  ledcWriteTone(ledcChannel, 90);    // Muted thump
  delay(150);
  ledcWriteTone(ledcChannel, 0);     // Stop sound
  delay(100);
}

void showSuccessPattern() {
  // Show each note color in sequence quickly (flourish)
  for(int cycle = 0; cycle < 3; cycle++) {  // Repeat 3 times for effect
    for(int i = 0; i < NUM_LEDS; i++) {
      clearLEDs();
      showNoteColor(i);  // Show each note's color
      delay(100);        // Quick timing
    }
  }
  clearLEDs();
}

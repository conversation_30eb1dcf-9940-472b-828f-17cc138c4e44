#include <Arduino.h>

struct Note {
  String color;
  byte inputPin;
  byte order;
  int tone;
  bool played;
};

int melody[] = { 262, 294, 330, 349, 392, 440, 494, 523 };  // C D E F G A B C


const int buzzerPin = 21;     // GPIO13 (can be any PWM-capable GPIO)
const int ledcChannel = 0;    // One of 0–15
const int resolution = 100;   
Note notes[] = {{"Blue", 12, 0, 392, false},
                {"Yellow", 27, 1, 330, false},
                {"Green", 14, 2, 349, false},
                {"Red", 32, 3, 262, false},
                {"Purple", 13, 4, 440, false},
                {"Orange", 33, 5, 294, false},};
const byte numNotes = sizeof(notes) / sizeof(notes[0]);

enum PuzzleState {
  Inactive,
  InProgress,
  Solved,
  Failed
};
PuzzleState puzzleState = Inactive;

unsigned long lastStateChange = 0;
unsigned long failIndicatorDuration = 2000;
unsigned long successIndicatorDuration = 2000;

const int threshold = 20;

void setupNotes();
void startPuzzle();
void runPuzzle();
void puzzleSolved();
void puzzleFailed();
void deactivatePuzzle();
void updateState(PuzzleState newState);
void resetPuzzle();
void playUglyFailureSound();
void playTone(int frequency, int duration);
int currentNoteIndex();
int lastNoteIndex();
int nextNoteIndex();
Note getNoteByOrder(int order);


void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  ledcSetup(ledcChannel, 2000, resolution);  // 2kHz tone, 8-bit resolution
  ledcAttachPin(buzzerPin, ledcChannel);

  setupNotes();
}

void setupNotes() {
  for (Note &note : notes) {
    pinMode(note.inputPin, INPUT);
  }
}

void loop() {
  // ledcWriteTone(ledcChannel, 3000);  // Play 1kHz tone
  // delay(500);                        // Beep duration
  // ledcWriteTone(ledcChannel, 0);     // Stop sound
  // delay(1000);


  switch (puzzleState) {
    case Inactive:
      // For now, start puzzle automatically - later you can add trigger condition
      startPuzzle();
      break;
    case InProgress:
      runPuzzle();
      break;
    case Solved:
      if (millis() - lastStateChange > successIndicatorDuration) {
        startPuzzle();
      }
      break;
    case Failed:
      if (millis() - lastStateChange > failIndicatorDuration) {
        startPuzzle();
      }
      break;
  }
}

void runPuzzle() {
  int currentNote = currentNoteIndex();
  int lastNote = lastNoteIndex();
  int nextNote = nextNoteIndex();

  if (nextNote < 0) {
    Serial.println("All notes played");
    puzzleSolved();
    return;
  }

  if (currentNote < 0) {
    return;
  }

  if (currentNote == nextNote) {
    Serial.println("Correct Note Played");
    notes[currentNote].played = true;
    ledcWriteTone(ledcChannel, notes[currentNote].tone);  // Play 3kHz tone
    delay(1000);                        // Beep duration
    ledcWriteTone(ledcChannel, 0);     // Stop sound
    // TODO: Play success sound
    return;
  }

  if (currentNote == lastNote) {
    return;
  }

  Serial.println("Wrong note played");
  playUglyFailureSound();
  for (Note &note : notes) {
    note.played = false;
  }
  puzzleFailed();
}



void startPuzzle() {
  updateState(InProgress);
  resetPuzzle();
  Serial.println("Puzzle started!");
}

void puzzleFailed() {
  updateState(Failed);
  Serial.println("Puzzle failed!");
  // TODO: Play failure sound
}

void puzzleSolved() {
  updateState(Solved);
  delay(100);
  for (Note &note : notes) {
    ledcWriteTone(ledcChannel, note.tone);  // Play 3kHz tone
    delay(500);                        // Beep duration
    ledcWriteTone(ledcChannel, 0);   
  }


  Serial.println("Puzzle solved!");
  // TODO: Play success sound
}

void deactivatePuzzle() {
  updateState(Inactive);
  resetPuzzle();
  Serial.println("Puzzle deactivated");
}

void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}

void resetPuzzle() {
  for (Note &note : notes) {
    note.played = false;
  }
}

int currentNoteIndex() {
  for (Note &note : notes) {
    if (touchRead(note.inputPin) < threshold) {
    // Serial.println(note.color);
     return note.order;
    }
  }
  return -1;
}

Note getNoteByOrder(int order) {
 for (Note &note : notes) {
    if (note.order == order) {
     return note;
    }
  }
  return Note{"", 0, 0, 0, false};
}

int lastNoteIndex() {
  int last = -1;
  for (Note &note : notes) {
    if (note.played) {
      last = note.order;
    }
  }
  return last;
}

int nextNoteIndex() {
  for (Note &note : notes) {
    if (!note.played) {
      return note.order;
    }
  }
  return -1;
}

void playUglyFailureSound() {
  // Create a harsh, discordant "ugly" failure sound using your original implementation

  // Harsh buzzing sound with rapid frequency changes
  ledcWriteTone(ledcChannel, 150);   // Very low, muddy tone
  delay(100);
  ledcWriteTone(ledcChannel, 666);   // Tritone (devil's interval) - very dissonant
  delay(80);
  ledcWriteTone(ledcChannel, 200);   // Low harsh buzz
  delay(120);
  ledcWriteTone(ledcChannel, 777);   // Another harsh frequency
  delay(90);
  ledcWriteTone(ledcChannel, 100);   // Even lower, more grinding
  delay(150);
  ledcWriteTone(ledcChannel, 555);   // Dissonant mid-range
  delay(100);
  ledcWriteTone(ledcChannel, 80);    // Very low rumble
  delay(200);

  // Add some rapid "glitchy" sounds
  for(int i = 0; i < 5; i++) {
    ledcWriteTone(ledcChannel, 300 + (i * 50));  // Quick ascending harsh tones
    delay(30);
  }

  // End with a long, low, unpleasant drone
  ledcWriteTone(ledcChannel, 120);
  delay(300);

  ledcWriteTone(ledcChannel, 0);     // Stop sound
  delay(100);  // Brief silence
}

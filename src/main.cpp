#include <Arduino.h>
#include <FastLED.h>

struct Note {
  String color;
  byte inputPin;
  byte order;
  int tone;
  bool played;
};

int melody[] = { 262, 294, 330, 349, 392, 440, 494, 523 };  // C D E F G A B C


const int buzzerPin = 21;     // GPIO21 for buzzer
const int ledcChannel = 0;    // One of 0–15
const int resolution = 100;

// LED Strip configuration
#define LED_PIN 25           // GPIO25 for LED strip data
#define NUM_LEDS 6          // Number of LEDs (one per note)
#define LED_TYPE WS2812B    // Common addressable LED type
#define COLOR_ORDER GRB     // Color order for WS2812B
CRGB leds[NUM_LEDS];
Note notes[] = {{"Blue", 12, 0, 392, false},
                {"Yellow", 27, 1, 330, false},
                {"Green", 14, 2, 349, false},
                {"Red", 32, 3, 262, false},
                {"Purple", 13, 4, 440, false},
                {"Orange", 33, 5, 294, false},};
const byte numNotes = sizeof(notes) / sizeof(notes[0]);

enum PuzzleState {
  Inactive,
  InProgress,
  Solved,
  Failed
};
PuzzleState puzzleState = Inactive;

unsigned long lastStateChange = 0;
unsigned long failIndicatorDuration = 2000;
unsigned long successIndicatorDuration = 2000;

const int threshold = 20;

void setupNotes();
void setupLEDs();
void startPuzzle();
void runPuzzle();
void puzzleSolved();
void puzzleFailed();
void deactivatePuzzle();
void updateState(PuzzleState newState);
void resetPuzzle();
void showNoteColor(int noteIndex);
void clearLEDs();
void showSuccessPattern();
int currentNoteIndex();
int lastNoteIndex();
int nextNoteIndex();
void playStringNote(int frequency, int duration);
void playStringChord();
Note getNoteByOrder(int order);


void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  ledcSetup(ledcChannel, 2000, resolution);  // 2kHz tone, 8-bit resolution
  ledcAttachPin(buzzerPin, ledcChannel);

  setupNotes();
  setupLEDs();
}

void setupNotes() {
  for (Note &note : notes) {
    pinMode(note.inputPin, INPUT);
  }
}

void loop() {
  // ledcWriteTone(ledcChannel, 3000);  // Play 1kHz tone
  // delay(500);                        // Beep duration
  // ledcWriteTone(ledcChannel, 0);     // Stop sound
  // delay(1000);


  switch (puzzleState) {
    case Inactive:
      // For now, start puzzle automatically - later you can add trigger condition
      startPuzzle();
      break;
    case InProgress:
      runPuzzle();
      break;
    case Solved:
      if (millis() - lastStateChange > successIndicatorDuration) {
        startPuzzle();
      }
      break;
    case Failed:
      if (millis() - lastStateChange > failIndicatorDuration) {
        startPuzzle();
      }
      break;
  }
}

void runPuzzle() {
  int currentNote = currentNoteIndex();
  int lastNote = lastNoteIndex();
  int nextNote = nextNoteIndex();

  if (nextNote < 0) {
    Serial.println("All notes played");
    puzzleSolved();
    return;
  }

  if (currentNote < 0) {
    return;
  }

  if (currentNote == nextNote) {
    Serial.println("Correct Note Played");
    notes[currentNote].played = true;
    showNoteColor(currentNote);         // Show LED color
    ledcWriteTone(ledcChannel, notes[currentNote].tone);  // Play tone
    delay(1000);                        // Beep duration
    ledcWriteTone(ledcChannel, 0);     // Stop sound
    clearLEDs();                        // Clear LED after note
    return;
  }

  if (currentNote == lastNote) {
    return;
  }

  Serial.println("Wrong note played");
  // TODO: Play failure sound
  for (Note &note : notes) {
    note.played = false;
  }
  puzzleFailed();
}



void startPuzzle() {
  updateState(InProgress);
  resetPuzzle();
  Serial.println("Puzzle started!");
}

void puzzleFailed() {
  updateState(Failed);
  Serial.println("Puzzle failed!");
}

void puzzleSolved() {
  updateState(Solved);
  Serial.println("Puzzle solved!");

  // Play through all correct notes again
  for (Note &note : notes) {
    showNoteColor(note.order);          // Show LED color
    ledcWriteTone(ledcChannel, note.tone);  // Play tone
    delay(500);                         // Note duration
    ledcWriteTone(ledcChannel, 0);      // Stop sound
    clearLEDs();                        // Clear LED
    delay(100);                         // Brief pause between notes
  }

  showSuccessPattern();               // Show LED flourish
  playStringChord();                  // Play success chord
}

void deactivatePuzzle() {
  updateState(Inactive);
  resetPuzzle();
  Serial.println("Puzzle deactivated");
}

void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}

void resetPuzzle() {
  for (Note &note : notes) {
    note.played = false;
  }
}

int currentNoteIndex() {
  for (Note &note : notes) {
    if (touchRead(note.inputPin) < threshold) {
    // Serial.println(note.color);
     return note.order;
    }
  }
  return -1;
}

Note getNoteByOrder(int order) {
 for (Note &note : notes) {
    if (note.order == order) {
     return note;
    }
  }
  return Note{"", 0, 0, 0, false};
}

int lastNoteIndex() {
  int last = -1;
  for (Note &note : notes) {
    if (note.played) {
      last = note.order;
    }
  }
  return last;
}

int nextNoteIndex() {
  for (Note &note : notes) {
    if (!note.played) {
      return note.order;
    }
  }
  return -1;
}


void playStringNote(int frequency, int duration) {
  // Simulate a plucked string with attack, sustain, and decay

  // Attack phase - start loud and bright
  ledcWriteTone(ledcChannel, frequency);
  delay(50);  // Initial bright attack

  // Add slight vibrato and natural decay
  int steps = duration / 50;
  for(int i = 0; i < steps; i++) {
    // Add subtle vibrato (frequency modulation)
    int vibrato = (i % 4 < 2) ? frequency + 2 : frequency - 2;
    ledcWriteTone(ledcChannel, vibrato);
    delay(50);
  }

  // Decay phase - fade out naturally
  ledcWriteTone(ledcChannel, 0);
  delay(100);  // Brief silence between notes
}

void playStringChord() {
  // Play a beautiful major chord arpeggio (like strumming)
  int chord[] = {262, 330, 392, 523};  // C major chord (C-E-G-C)

  // Strum the chord (quick succession)
  for(int i = 0; i < 4; i++) {
    ledcWriteTone(ledcChannel, chord[i]);
    delay(80);  // Quick strum timing
  }

  // Let the final note ring out with natural decay
  playStringNote(523, 800);  // High C with natural string decay
}

void setupLEDs() {
  FastLED.addLeds<LED_TYPE, LED_PIN, COLOR_ORDER>(leds, NUM_LEDS);
  FastLED.setBrightness(100);  // Set brightness (0-255)
  clearLEDs();
}

void showNoteColor(int noteIndex) {
  clearLEDs();  // Clear all LEDs first

  // Map note colors to RGB values
  CRGB color;
  String noteColor = notes[noteIndex].color;

  if (noteColor == "Red") {
    color = CRGB::Red;
  } else if (noteColor == "Orange") {
    color = CRGB::Orange;
  } else if (noteColor == "Yellow") {
    color = CRGB::Yellow;
  } else if (noteColor == "Green") {
    color = CRGB::Green;
  } else if (noteColor == "Blue") {
    color = CRGB::Blue;
  } else if (noteColor == "Purple") {
    color = CRGB::Purple;
  } else {
    color = CRGB::White;  // Default color
  }

  // Light up the LED corresponding to the note
  leds[noteIndex] = color;
  FastLED.show();
}

void clearLEDs() {
  fill_solid(leds, NUM_LEDS, CRGB::Black);
  FastLED.show();
}

void showSuccessPattern() {
  // Show each note color in sequence quickly (flourish)
  for(int cycle = 0; cycle < 3; cycle++) {  // Repeat 3 times for effect
    for(int i = 0; i < NUM_LEDS; i++) {
      clearLEDs();
      showNoteColor(i);  // Show each note's color
      delay(100);        // Quick timing
    }
  }
  clearLEDs();
}
